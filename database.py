import os
from dotenv import load_dotenv

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base 


load_dotenv()
database_user = os.getenv("DBUSER")
database_password = os.getenv("DBPASSWORD")

URL_DATABASE = f'mysql+pymysql://{database_user}:{database_password}@127.0.0.1:3306/api_service'


engine = create_engine(URL_DATABASE)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()