import requests
import urllib3
import models
import datetime
from fastapi import FastAPI, HTTPException, Depends, status, APIRouter
from pydantic import BaseModel
from typing import Annotated, Optional
from database import engine, SessionLocal
from sqlalchemy.orm import Session

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


# atea_baas = FastAPI()
atea_baas = FastAPI(
    title="DCES Core Services BaaS API service",
    root_path="/api/v1",
    root_path_in_servers=True,
    verify_ssl=False,
)

models.Base.metadata.create_all(bind=engine)

# This will change my default base url to https://localhost:8000/api/
# api_router = APIRouter(prefix="/api")


class UserBase(BaseModel):
    username: str


class BackupRequestBase(BaseModel):
    service_request_id: str
    server: str
    db_type: str
    backup_time: int
    full_backup: str
    environment: Optional[str] = None # this field is optional only
    owner: str


def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


db_dependency = Annotated[Session, Depends(get_db)]

@atea_baas.get("/", status_code=status.HTTP_200_OK)
async def root():
    return {"message": "Welcome to DCES Core Services Backup API service"}

@atea_baas.post("/users/", status_code=status.HTTP_201_CREATED)
async def create_user(user: UserBase, db: db_dependency):
    db_user = models.User(**user.dict())
    db.add(db_user)
    db.commit()


@atea_baas.post("/backup_request/", status_code=status.HTTP_201_CREATED)
async def create_backup_request(bkp_request: BackupRequestBase, db: db_dependency):
    backupRequest = models.BackupRequest(**bkp_request.dict())
    db.add(backupRequest)
    db.commit()
    return bkp_request
    
@atea_baas.get("/backup_request/{service_request_id}", status_code=status.HTTP_200_OK)
async def get_backup_request(service_request_id: str, db: db_dependency):
    backupRequest = db.query(models.BackupRequest).filter(models.BackupRequest.service_request_id == service_request_id).first()
    if backupRequest is None:
        raise HTTPException(status_code=404, detail="Backup request not found")
    return backupRequest





# Include the API router with prefix "/api" in my atea_baas application
# atea_baas.include_router(api_router)
