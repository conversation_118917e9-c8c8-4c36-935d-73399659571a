from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, Integer, String, DateTime
from database import Base
from datetime import datetime

class User(Base):
    __tablename__ = "users"
    
    ID = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True)
    
    
class Post(Base):
    __tablename__ = "posts"
    
    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(50), unique=True)
    content = Column(String(100))
    userid = Column(Integer)


class BackupRequest(Base):
    __tablename__ = "baas_request"
    
    id = Column(Integer, primary_key=True, index=True)
    service_request_id = Column(String(32), unique=True)
    server = Column(String(32), unique=True)
    db_type = Column(String(32))
    backup_time = Column(Integer)
    full_backup = Column(String(12))
    environment = Column(String(12), nullable=True)
    owner = Column(String(32))
    created_at = Column(DateTime, default=datetime.now())

    

