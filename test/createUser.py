import requests
import json
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

url = "https://dkcdctestcs1.vestas.net/commandcenter/api/V4/user"

payload = json.dumps({
  "users": [
    {
      "fullName": "test1",
      "name": "test1",
      "externalProviderId": 0,
      "email": "<EMAIL>",
      "company": {
        "id": 0,
        "name": "test-company"
      },
      "userGroups": [
        {
          "id": 0,
          "name": "master"
        }
      ],
      "useSystemGeneratePassword": True,
      "password": "string",
      "inviteUser": True,
      "plan": {
        "id": 0,
        "name": "string"
      }
    }
  ]
})

headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'Authtoken': 'QSDK 332a062ad89e2bdf34fe538c392303126abaeca55787ce236c511a577399645caf46104086eb6674399c0f5fd22733fdf297d717ffa8aa5ddcae216a23062a9c109bcf0617af3f42dcaffffc339c8c7ae3f42a73b94dc660fe46715ef0cc8b423990a8fd4f4eab1b270c49c2f87130e6809b3fe35ec26892531a181cd801fb52050eadde4ab0b22ed4ada15a6fa2820d4470e665534c0ffe682f5474c129c6a6970abe064a45cd3c555acae56301a43ad1cbf30195af1b7e1a8dfb29deabceb035246cb2ac152cbc9e8aa0d407021d77b6e7966bbaa7de472'
}

response = requests.request("POST", url, headers=headers, data=payload, verify=False)

print(response.text)

if response.status_code == 200:
    # Convert the response content to a Python dictionary
    json_data = response.json()
    
    # If you want to print it nicely formatted
    print(json.dumps(json_data, indent=4))
else:
    print(f"Request failed with status code: {response.status_code}")
    
    
    
''' 
The creation of user was successful but there is an error about SMTP as there is no configure smtp server.
Below is the error message.

[svc-dkcdc-sanstobkp@dkcdcssanbkp01 commvault]$ python createUser.py 
{"failedStage":"InviteUser","errorCode":-1,"errorMessage":{"processinginstructioninfo":{"attributes":[{"name":"exitval","value":"1"}]},"errorMessage":"No host is specified. smtpServer is not set.","errorCode":"1"}}
Request failed with status code: 500
'''