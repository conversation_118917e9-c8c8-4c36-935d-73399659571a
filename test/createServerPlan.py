import requests
import json
import urllib3

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

url = "https://dkcdctestcs1.vestas.net/commandcenter/api/V4/ServerPlan"

payload = json.dumps({
  "planName": "VESTAS_PLAN_SILVER",
  "backupDestinations": [
    {
      "backupDestinationName": "Primary",
      "retentionPeriodDays": 7,
      "useExtendedRetentionRules": True,
      "overrideRetentionSettings": True,
      "backupStartTime": -1,
      "storagePool": {
        "id": 3,
        "name": "data01"
      },
      "storageType": "DISK",
      "extendedRetentionRules": {
        "firstExtendedRetentionRule": {
          "isInfiniteRetention": False,
          "type": "WEEKLY_FULLS",
          "retentionPeriodDays": 21
        },
        "secondExtendedRetentionRule": {
          "type": "MONTHLY_FULLS",
          "retentionPeriodDays": 90,
          "isInfiniteRetention": False
        }
      }
    }
  ],
  "rpo": {
    "backupFrequency": {
      "schedules": [
        {
          "backupType": "INCREMENTAL",
          "scheduleOperation": "ADD",
          "schedulePattern": {
            "scheduleFrequencyType": "DAILY",
            "startTime": 75600,
            "exceptions": [
              {
                "onDayOfTheWeek": [
                  "SUNDAY"
                ],
                "onWeekOfTheMonth": [
                  "FIRST",
                  "SECOND",
                  "THIRD",
                  "FOURTH"
                ]
              },
              {
                "onDayOfTheWeek": [
                  "SUNDAY"
                ],
                "onWeekOfTheMonth": [
                  "LAST"
                ]
              }
            ],
            "frequency": 1,
            "timezone": {
              "id": 1001
            }
          },
          "forDatabasesOnly": False,
          "scheduleOption": {
            "daysBetweenAutoConvert": 7
          }
        },
        {
          "backupType": "TRANSACTIONLOG",
          "scheduleOperation": "ADD",
          "schedulePattern": {
            "scheduleFrequencyType": "AUTOMATIC",
            "maxBackupIntervalInMins": 240
          },
          "forDatabasesOnly": True,
          "scheduleOption": {
            "useDiskCacheForLogBackups": False,
            "commitFrequencyInHours": 8,
            "logsDiskUtilizationPercent": 80,
            "logFilesThreshold": 50
          }
        },
        {
          "backupType": "SYNTHETICFULL",
          "scheduleOperation": "ADD",
          "schedulePattern": {
            "scheduleFrequencyType": "AUTOMATIC",
            "startTime": 75600,
            "frequency": 90,
            "daysBetweenSyntheticFulls": 90
          },
          "forDatabasesOnly": False,
          "scheduleName": "Synthetic Fulls"
        },
        {
          "backupType": "FULL",
          "scheduleOperation": "ADD",
          "schedulePattern": {
            "scheduleFrequencyType": "WEEKLY",
            "startTime": 75600,
            "exceptions": [
              {
                "onDayOfTheWeek": [
                  "SUNDAY"
                ],
                "onWeekOfTheMonth": [
                  "LAST"
                ]
              }
            ],
            "frequency": 1,
            "weeklyDays": [
              "SUNDAY"
            ],
            "timezone": {
              "id": 1001
            }
          },
          "forDatabasesOnly": False,
          "scheduleOption": {
            "daysBetweenAutoConvert": 7
          }
        },
        {
          "backupType": "FULL",
          "scheduleOperation": "ADD",
          "schedulePattern": {
            "scheduleFrequencyType": "MONTHLY",
            "startTime": 75600,
            "frequency": 1,
            "weekOfMonth": "LAST",
            "dayOfWeek": "SUNDAY"
          },
          "forDatabasesOnly": False
        }
      ]
    },
    "backupWindow": [],
    "fullBackupWindow": []
  },
  "allowPlanOverride": True,
  "overrideRestrictions": {
    "storagePool": "NOT_ALLOWED",
    "RPO": "OPTIONAL",
    "archivingRules": "OPTIONAL"
  }
})

headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json',
  'Authtoken': 'QSDK 36c8fddf061b4efd249ba8e8ec4a8943032f4373172ebc60bd865855e6fdb866fc9d2aa09412b33419a21459401439196177b7e9464f2985c9148ddf117ee32f5b3e2a460db3a44afb23538faf28f97b9f187a0fa9ae085a9fc18db994719ffc5b80e3c0461ccaf4bbcc3d2962239e1ceb9ba5e6281154bdb73de42521cc7c0740f2382f7650f8ffa6be4319aa918b93994ad741ddaad61cbf017967eab9d52f8a6153e46eeae5dc600a89bd67c5b6912bb8226276d8ccdc6f22de28309c3f7982c414e555c6efc85d37b75d39db2d5a92bff18b7770350da'
}

response = requests.request("POST", url, headers=headers, data=payload, verify=False)

print(response.text)
print(response.status_code)