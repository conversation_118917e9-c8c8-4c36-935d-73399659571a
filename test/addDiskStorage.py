

payload = jason.dumps({
  "storagePolicyName": "data01",
  "copyName": "Primary",
  "type": "CVA_REGULAR_SP",
  "numberOfCopies": 1,
  "storagePolicyCopyInfo": {
    "copyType": "SYNCHRONOUS",
    "isDefault": "SET_TRUE",
    "active": "SET_TRUE",
    "storagePolicyFlags": {
      "blockLevelDedup": "SET_TRUE",
      "enableGlobalDeduplication": "SET_TRUE"
    },
    "library": {
      "libraryId": 0
    },
    "mediaAgent": {
      "mediaAgentId": 2,
      "mediaAgentName": "dkcdctestcs1"
    },
    "retentionRules": {
      "retentionFlags": {
        "enableDataAging": "SET_TRUE"
      },
      "retainBackupDataForDays": -1,
      "retainBackupDataForCycles": -1,
      "retainArchiverDataForDays": -1
    },
    "isFromGui": true,
    "numberOfStreamsToCombine": 1,
    "dedupeFlags": {
      "enableDeduplication": "SET_TRUE",
      "enableDASHFull": "SET_TRUE",
      "hostGlobalDedupStore": "SET_TRUE"
    },
    "DDBPartitionInfo": {
      "maInfoList": [
        {
          "mediaAgent": {
            "mediaAgentId": 2,
            "mediaAgentName": "dkcdctestcs1"
          },
          "subStoreList": [
            {
              "accessPath": {
                "path": "F:\\DDB01"
              },
              "diskFreeThresholdMB": 5120,
              "diskFreeWarningThreshholdMB": 10240
            }
          ]
        }
      ],
      "sidbStoreInfo": {
        "numSIDBStore": 2
      }
    }
  },
  "clientGroup": {
    "clientGroupId": 0
  },
  "storage": [
    {
      "mediaAgent": {
        "mediaAgentId": 2,
        "mediaAgentName": "dkcdctestcs1"
      },
      "path": "F:\\Data\\mountpath01",
      "credentials": {
        "userName": ""
      }
    }
  ]
})