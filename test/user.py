import requests
import urllib3
import json
from pprint import pprint

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

url = "https://dkcdctestcs1.vestas.net/commandcenter/api/V4/user"

payload={}
headers = {
  'Accept': 'application/json',
  'Authtoken': 'QSDK 36c8fddf061b4efd249ba8e8ec4a8943032f4373172ebc60bd865855e6fdb866fc9d2aa09412b33419a21459401439196177b7e9464f2985c9148ddf117ee32f5b3e2a460db3a44afb23538faf28f97b9f187a0fa9ae085a9fc18db994719ffc5b80e3c0461ccaf4bbcc3d2962239e1ceb9ba5e6281154bdb73de42521cc7c0740f2382f7650f8ffa6be4319aa918b93994ad741ddaad61cbf017967eab9d52f8a6153e46eeae5dc600a89bd67c5b6912bb8226276d8ccdc6f22de28309c3f7982c414e555c6efc85d37b75d39db2d5a92bff18b7770350da'
}

response = requests.request("GET", url, headers=headers, data=payload, verify=False)

# Convert the response content to a Python dictionary
json_data = response.json()

# Now you can work with the data like a dictionary
# print(json_data['users'])

# Print in json format
print(json.dumps(json_data, indent=4))