import requests
import json
from pprint import pprint
import urllib3

# Suppress SSL warnings for self-signed certificates in a lab environment
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


url = "https://dkcdctestcs1.vestas.net/commandcenter/api/Login"

payload = json.dumps({
  "username": "admin",
  "password": "UEBzc3cwcmQ="
})
headers = {
  'Content-Type': 'application/json',
  'Accept': 'application/json'
}

response = requests.request("POST", url, headers=headers, data=payload, verify=False)

pprint(response.text)